<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarTitleText": "充值记录"
    }
}</route>

<script lang="ts" setup>
import { getPrepaidCardRechargeLog } from '@/api/prepaidCard';

import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import UseZPaging from "z-paging/components/z-paging/js/hooks/useZPaging"
import { useEnumStore } from '@/store';
const enumStore = useEnumStore();

const scrollRef = ref(null);
UseZPaging(scrollRef)
const listData = ref<any>([]);
const getList = async (pageNo: number, pageSize: number = 10) => {
    try {
        const res: any = await getPrepaidCardRechargeLog({
            page: pageNo,
            pageSize: pageSize,
        })
        if (res.code === 200) {
            scrollRef.value?.complete(res.data.list)
        } else {
            scrollRef.value?.complete(false)
            uni.showToast({
                title: '获取数据失败',
                icon: 'none'
            })
        }
    } catch (error) {
        scrollRef.value?.complete(false)
        uni.showToast({
            title: '网络请求异常',
            icon: 'none'
        })
    }
}

const filterText = (val: any) => {
    let arr = enumStore.enumData?.orderStatus.list
    return arr.find(item => item.value === val)?.label
}
const typeMap = {
    1: 'warning',
    2: 'success',
    3: 'primary',
    4: 'danger',
    5: 'default',
    6: 'default',
}
</script>

<template>
    <z-paging ref="scrollRef" v-model="listData" @query="getList" :safe-area-inset-bottom="true"
        :use-safe-area-placeholder="true" :show-loading-more-no-more-view="false">
        <view class="p-3">
            <view class="bg-white p-2 rounded mb-3" v-for="item in listData" :key="item.id">
                <view class="flex items-center justify-between">
                    <view class="text-md font-bold">账户充值</view>
                    <wd-tag :type="typeMap[item.orderStatus]" custom-class="!px-1.5 !py-0.5" plain round>{{
                        filterText(item.orderStatus) }}</wd-tag>
                </view>
                <view class="flex justify-between items-center">
                    <view>
                        <view class="text-xs text-gray-500 mt-2">订单号：{{ item.orderNo }}</view>
                        <view class="text-xs text-gray-500 mt-2">充值时间：{{ item.createdAt }}</view>
                    </view>
                    <view class="text-[#fa4350] font-bold text-center">
                        <view>+{{ item.amount }}</view>
                        <view class="text-xs" v-if="item.giftAmount > 0">（赠：{{ item.giftAmount }}）</view>
                    </view>
                </view>

            </view>
        </view>
    </z-paging>
</template>

<style lang="scss" scoped></style>
