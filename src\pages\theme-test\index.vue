<route lang="jsonc">{
  "style": {
    "navigationBarTitleText": "主题测试"
  }
}</route>

<script setup lang="ts">
import { useThemeStore } from '@/store'

const themeStore = useThemeStore()

const toggleTheme = () => {
  themeStore.toggleTheme()
}

const setLightTheme = () => {
  themeStore.setTheme('light')
}

const setDarkTheme = () => {
  themeStore.setTheme('dark')
}
</script>

<template>
  <view class="min-h-screen bg-white dark:bg-gray-900 px-4 py-8">
    <view class="text-center mb-8">
      <text class="text-2xl font-bold text-gray-900 dark:text-white">
        主题测试页面
      </text>
    </view>

    <view class="mb-6">
      <text class="text-lg text-gray-700 dark:text-gray-300">
        当前主题: {{ themeStore.theme }}
      </text>
    </view>

    <view class="mb-6">
      <text class="text-lg text-gray-700 dark:text-gray-300">
        是否为暗黑模式: {{ themeStore.isDark }}
      </text>
    </view>

    <view class="space-y-4">
      <wd-button 
        type="primary" 
        block 
        @click="toggleTheme"
      >
        切换主题
      </wd-button>

      <wd-button 
        type="success" 
        block 
        @click="setLightTheme"
      >
        设置浅色主题
      </wd-button>

      <wd-button 
        type="warning" 
        block 
        @click="setDarkTheme"
      >
        设置暗黑主题
      </wd-button>
    </view>

    <!-- 测试各种暗黑模式样式 -->
    <view class="mt-8 space-y-4">
      <view class="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <text class="text-gray-900 dark:text-white">
          这是一个测试容器，背景色会根据主题变化
        </text>
      </view>

      <view class="p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
        <text class="text-gray-700 dark:text-gray-300">
          这是带边框的容器，边框颜色会根据主题变化
        </text>
      </view>

      <view class="grid grid-cols-2 gap-4">
        <view class="p-3 bg-blue-100 dark:bg-blue-900 rounded">
          <text class="text-blue-900 dark:text-blue-100">蓝色主题</text>
        </view>
        <view class="p-3 bg-green-100 dark:bg-green-900 rounded">
          <text class="text-green-900 dark:text-green-100">绿色主题</text>
        </view>
        <view class="p-3 bg-red-100 dark:bg-red-900 rounded">
          <text class="text-red-900 dark:text-red-100">红色主题</text>
        </view>
        <view class="p-3 bg-yellow-100 dark:bg-yellow-900 rounded">
          <text class="text-yellow-900 dark:text-yellow-100">黄色主题</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gap-4 {
  gap: 1rem;
}
</style>
