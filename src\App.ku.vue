<script setup lang="ts">
import { ref } from 'vue'
import { useThemeStore } from '@/store'
import FgTabbar from '@/tabbar/index.vue'
import { isPageTabbar } from './tabbar/store'
import { currRoute } from './utils'
import { useTheme } from '@/composables/useTheme'

// const themeStore = useThemeStore()
const { themeVars, theme, isDark } = useTheme()

const isCurrentPageTabbar = ref(true)
onShow(() => {
  console.log('App.ku.vue onShow', currRoute())
  const { path } = currRoute()
  isCurrentPageTabbar.value = isPageTabbar(path)
})

const helloKuRoot = ref('Hello AppKuVue')

const exposeRef = ref('this is form app.Ku.vue')

defineExpose({
  exposeRef,
})
</script>

<template>
  <!-- 这个先隐藏了，知道这样用就行 -->
  <view class="text-center">
    {{ helloKuRoot }}，这里可以配置全局的东西 主题：{{ theme }} {{ isDark }}
  </view>
  <wd-config-provider :theme-vars="themeVars" :theme="theme">
    <KuRootView />
  </wd-config-provider>

  <FgTabbar v-if="isCurrentPageTabbar" />
  <wd-toast />
  <wd-message-box />
</template>
